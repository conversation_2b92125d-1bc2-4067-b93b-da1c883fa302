/**
 * ClientContactInformation Value Object
 *
 * Represents the contact information for a client in the presentation subdomain.
 * This value object encapsulates all contact-related data and ensures data integrity
 * through domain validation rules.
 *
 * As a value object, instances are immutable - any changes require creating a new instance.
 */

export interface ClientContactInformationProps {
  mainEmail: string;
  mainPhoneNumber?: string;
  mainWhatsapp?: string;
  mainAddress?: string;
  representativeName?: string;
  billingEmail?: string;
  billingPhoneNumber?: string;
  billingWhatsapp?: string;
  billingAddress?: string;
  purchasesEmail?: string;
  purchasesPhoneNumber?: string;
  purchasesWhatsapp?: string;
  salesEmail?: string;
  salesPhoneNumber?: string;
  salesWhatsapp?: string;
  shippingAddress?: string;
}

export class ClientContactInformation {
  private readonly props: ClientContactInformationProps;

  constructor(props: ClientContactInformationProps) {
    this.validateProps(props);
    this.props = { ...props };
  }

  private validateProps(props: ClientContactInformationProps): void {
    // Domain validation: mainEmail is required and must be valid
    if (!props.mainEmail || props.mainEmail.trim() === '') {
      throw new Error('El email principal es requerido para la información de contacto del cliente');
    }

    if (!this.isValidEmail(props.mainEmail)) {
      throw new Error('El email principal debe tener un formato válido');
    }

    // Validate other emails if provided
    const emailFields = ['billingEmail', 'purchasesEmail', 'salesEmail'] as const;
    for (const field of emailFields) {
      const email = props[field];
      if (email && !this.isValidEmail(email)) {
        throw new Error(`El campo ${field} debe tener un formato de email válido`);
      }
    }

    // Validate phone numbers if provided
    const phoneFields = [
      'mainPhoneNumber', 'billingPhoneNumber', 'purchasesPhoneNumber',
      'salesPhoneNumber', 'mainWhatsapp', 'billingWhatsapp',
      'purchasesWhatsapp', 'salesWhatsapp',
    ] as const;

    for (const field of phoneFields) {
      const phone = props[field];
      if (phone && !this.isValidPhoneNumber(phone)) {
        throw new Error(`El campo ${field} debe tener un formato de teléfono válido`);
      }
    }

    // Validate addresses if provided
    const addressFields = ['mainAddress', 'billingAddress', 'shippingAddress'] as const;
    for (const field of addressFields) {
      const address = props[field];
      if (address && address.trim().length < 5) {
        throw new Error(`El campo ${field} debe tener al menos 5 caracteres`);
      }
    }

    // Validate representative name if provided
    if (props.representativeName && props.representativeName.trim().length < 2) {
      throw new Error('El nombre del representante debe tener al menos 2 caracteres');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPhoneNumber(phone: string): boolean {
    // Allow numbers with optional + prefix and spaces/dashes
    const phoneRegex = /^\+?[\d\s\-()]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 7;
  }

  // Getters for accessing properties
  get mainEmail(): string {
    return this.props.mainEmail;
  }

  get mainPhoneNumber(): string | undefined {
    return this.props.mainPhoneNumber;
  }

  get mainWhatsapp(): string | undefined {
    return this.props.mainWhatsapp;
  }

  get mainAddress(): string | undefined {
    return this.props.mainAddress;
  }

  get representativeName(): string | undefined {
    return this.props.representativeName;
  }

  get billingEmail(): string | undefined {
    return this.props.billingEmail;
  }

  get billingPhoneNumber(): string | undefined {
    return this.props.billingPhoneNumber;
  }

  get billingWhatsapp(): string | undefined {
    return this.props.billingWhatsapp;
  }

  get billingAddress(): string | undefined {
    return this.props.billingAddress;
  }

  get purchasesEmail(): string | undefined {
    return this.props.purchasesEmail;
  }

  get purchasesPhoneNumber(): string | undefined {
    return this.props.purchasesPhoneNumber;
  }

  get purchasesWhatsapp(): string | undefined {
    return this.props.purchasesWhatsapp;
  }

  get salesEmail(): string | undefined {
    return this.props.salesEmail;
  }

  get salesPhoneNumber(): string | undefined {
    return this.props.salesPhoneNumber;
  }

  get salesWhatsapp(): string | undefined {
    return this.props.salesWhatsapp;
  }

  get shippingAddress(): string | undefined {
    return this.props.shippingAddress;
  }

  // Method to create a new instance with updated properties
  public updateWith(updates: Partial<ClientContactInformationProps>): ClientContactInformation {
    return new ClientContactInformation({
      ...this.props,
      ...updates,
    });
  }

  // Method to convert to plain object (useful for serialization)
  public toPlainObject(): ClientContactInformationProps {
    return { ...this.props };
  }

  // Equality comparison
  public equals(other: ClientContactInformation): boolean {
    if (!(other instanceof ClientContactInformation)) {
      return false;
    }

    return JSON.stringify(this.props) === JSON.stringify(other.props);
  }
}
