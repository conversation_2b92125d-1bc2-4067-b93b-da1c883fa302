import { ClientContactInformation } from './ClientContactInformation.ValueObject';

/**
 * Client Aggregate Root - Presentation Subdomain
 *
 * SUBDOMAIN DEFINITION:
 * In the presentation subdomain, a Client represents a business entity or individual
 * that has a commercial relationship with our company. This differs from other subdomains
 * where a client might be represented differently (e.g., in billing, a client might focus
 * on payment methods and credit limits).
 *
 * BUSINESS MEANING:
 * A client in this context represents:
 * - A potential customer (prospecto) who might place orders
 * - An active customer (activo) who regularly purchases from us
 * - An inactive customer (inactivo) who has stopped purchasing but maintains the relationship
 * - A legal entity with tax identification (tributaryId) for invoicing purposes
 * - A contact point with structured communication channels (email, phone, WhatsApp)
 * - A beneficiary of store-wide discounts based on business agreements
 *
 * AGGREGATE BOUNDARIES:
 * This aggregate includes:
 * - Client identity and basic information
 * - Contact information (as a value object)
 * - References to store discounts (by ID only, following DDD principles)
 * - Business relationship metadata (creation date, company association)
 *
 * DOMAIN OPERATIONS:
 * - Update contact information (immutable value object replacement)
 * - Manage store discount assignments
 * - Update basic client information with domain validations
 *
 * FUTURE MIGRATION NOTES:
 * - storeDiscounts should reference StoreDiscount entities by ID only
 * - Application layer should construct full ApplicationClient when needed
 * - Repository layer handles transparent loading of related entities
 */

export enum ClientType {
  CLIENT = 'client',
  COMPANY = 'company'
}

// Legacy type for backward compatibility - will be deprecated
export type ContactInformation = {
  mainEmail: string;
  mainPhoneNumber?: string;
  mainWhatsapp?: string;
  mainAddress?: string;
  representativeName?: string;
  billingEmail?: string;
  billingPhoneNumber?: string;
  billingWhatsapp?: string;
  billingAddress?: string;
  purchasesEmail?: string;
  purchasesPhoneNumber?: string;
  purchasesWhatsapp?: string;
  salesEmail?: string;
  salesPhoneNumber?: string;
  salesWhatsapp?: string;
  shippingAddress?: string;
};

// Legacy type for backward compatibility - will be deprecated
export type Client = {
  id: string;
  name: string;
  tributaryId?: string | null;
  clientCompany?: { name?: string } | null;
  contactInformation?: Partial<ContactInformation> | null;
  city?: string;
  status?: 'activo' | 'prospecto' | 'inactivo';
  conversations: number;
  tags?: string[];
  lastContact?: string;
  createdAt?: string;
  updatedAt?: string;
  notes?: string;
  clientCompanyId?: string | null;
};

export default class ClientEntity {
  readonly id: string;

  private name: string;

  private tributaryId: string;

  private clientCompanyId: string | null;

  private storeDiscountIds: string[]; // TODO: Migrate to reference store discounts by ID only

  private contactInformation: ClientContactInformation | null;

  readonly createdAt: Date;

  constructor(
    id: string,
    name: string,
    tributaryId: string,
    clientCompanyId: string | null,
    storeDiscountIds: string[],
    contactInformation: ClientContactInformation | null,
    createdAt: Date,
  ) {
    // Domain validations
    if (!id || id.trim() === '') {
      throw new Error('El ID del cliente es requerido');
    }
    if (!name || name.trim() === '') {
      throw new Error('El nombre del cliente es requerido');
    }
    if (!tributaryId || tributaryId.trim() === '') {
      throw new Error('El ID tributario del cliente es requerido');
    }

    this.id = id;
    this.name = name.trim();
    this.tributaryId = tributaryId.trim();
    this.clientCompanyId = clientCompanyId;
    this.storeDiscountIds = [...storeDiscountIds];
    this.contactInformation = contactInformation;
    this.createdAt = createdAt;
  }

  // Getters for accessing private properties
  getName(): string {
    return this.name;
  }

  getTributaryId(): string {
    return this.tributaryId;
  }

  getClientCompanyId(): string | null {
    return this.clientCompanyId;
  }

  getStoreDiscountIds(): string[] {
    return [...this.storeDiscountIds];
  }

  getContactInformation(): ClientContactInformation | null {
    return this.contactInformation;
  }

  // Domain operations
  public setContactInformation(contactInformation: ClientContactInformation): void {
    this.contactInformation = contactInformation;
  }

  public updateName(name: string): void {
    if (!name || name.trim() === '') {
      throw new Error('El nombre del cliente no puede estar vacío');
    }
    this.name = name.trim();
  }

  public updateTributaryId(tributaryId: string): void {
    if (!tributaryId || tributaryId.trim() === '') {
      throw new Error('El ID tributario del cliente no puede estar vacío');
    }
    this.tributaryId = tributaryId.trim();
  }

  public assignStoreDiscount(storeDiscountId: string): void {
    if (!storeDiscountId || storeDiscountId.trim() === '') {
      throw new Error('El ID del descuento de tienda es requerido');
    }
    if (!this.storeDiscountIds.includes(storeDiscountId)) {
      this.storeDiscountIds.push(storeDiscountId);
    }
  }

  public removeStoreDiscount(storeDiscountId: string): void {
    this.storeDiscountIds = this.storeDiscountIds.filter((id) => id !== storeDiscountId);
  }

  public hasStoreDiscount(storeDiscountId: string): boolean {
    return this.storeDiscountIds.includes(storeDiscountId);
  }

  // Legacy compatibility - will be deprecated
  get storeDiscounts(): string[] {
    return this.getStoreDiscountIds();
  }

  get updatedAt(): Date {
    // For backward compatibility, return createdAt as updatedAt is not useful in FE
    return this.createdAt;
  }
}
